# Contributing to SCE Portal

Thank you for your interest in contributing to the SCE Portal project! This guide will help you get started with our development workflow and collaboration processes.

## 📚 Essential Documentation

Before contributing, please review our comprehensive documentation:

- **[📖 Documentation Index](../docs/README.md)** - Complete documentation overview
- **[⚡ Quick Start Guide](../docs/QUICK_START.md)** - Get set up in 15 minutes
- **[🤖 AI Agent Workflow](../docs/AI_AGENT_WORKFLOW.md)** - Detailed collaboration processes
- **[💻 Command Reference](../docs/COMMAND_REFERENCE.md)** - Essential development commands

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- pnpm (required package manager)
- Git
- GitHub account with repository access

### Setup
```bash
git clone https://github.com/stcloudenterprises/sce_portal.git
cd sce_portal
pnpm install
cp .env.example .env.local
# Configure your .env.local with OAuth credentials
pnpm dev
```

## 🔄 Development Workflow

### Standard Contribution Process
1. **Update main branch**: `git checkout main && git pull origin main`
2. **Create feature branch**: `git checkout -b feature/your-feature-name`
3. **Develop and test**: Implement your changes with proper testing
4. **Quality assurance**: Run `pnpm lint && pnpm type-check && pnpm test && pnpm build`
5. **Commit changes**: Use proper attribution (see below)
6. **Push and create PR**: `git push origin feature/your-feature-name`

### Git Attribution Requirements

#### For AI Agents
```bash
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "feat: your message"
```

#### For Human Contributors
Use your own Git configuration:
```bash
git commit -m "feat: your message"
```

## 🤖 AI Agent Collaboration

### Agent Types and Capabilities

#### Local Agents
- ✅ Full API testing with sensitive environment variables
- ✅ OAuth flow testing
- ✅ Email functionality testing
- ✅ Database operations
- ✅ Complete integration testing

#### Remote Agents
- ✅ UI/UX changes and styling
- ✅ Component development and refactoring
- ✅ TypeScript/JavaScript logic changes
- ✅ Build configuration updates
- ❌ Limited API testing without secrets

### "New Task" Protocol
When an AI agent begins work, start the message with:
```
New Task: [description of the task]
```
This triggers proper branch creation and Git attribution setup.

## 📋 Code Quality Standards

### Required Checks
All contributions must pass:
```bash
pnpm lint          # ESLint validation
pnpm type-check    # TypeScript validation
pnpm test          # Test suite
pnpm build         # Production build verification
```

### Code Style
- Use TypeScript for all new code
- Follow existing code formatting (Prettier configured)
- Write tests for new functionality
- Update documentation for significant changes

## 🔐 Security Guidelines

### Environment Variables
- **Never commit actual secrets** to the repository
- Use template syntax: `VARIABLE="${VARIABLE:-default-value}"`
- Keep `.env.local` files local and gitignored
- Use different secrets for development and production

### Authentication
- Test all authentication flows thoroughly
- Verify OAuth redirect URIs are correctly configured
- Ensure proper session management
- Follow security best practices for user data

## 🧪 Testing Requirements

### Test Coverage
- Write unit tests for new components and functions
- Include integration tests for authentication flows
- Test error handling and edge cases
- Maintain existing test coverage levels

### Testing Commands
```bash
pnpm test                    # Run all tests
pnpm test:watch             # Watch mode for development
pnpm test:coverage          # Generate coverage report
```

## 📝 Pull Request Guidelines

### PR Requirements
- **Clear description** of changes and motivation
- **Link to related issues** if applicable
- **Screenshots** for UI changes
- **Test results** showing all checks pass
- **Documentation updates** for significant changes

### PR Review Process
1. **Automated checks** must pass (lint, type-check, test, build)
2. **Human review** for code quality and architecture
3. **Testing verification** for authentication and API changes
4. **Documentation review** for accuracy and completeness

### PR Title Format
Use conventional commit format:
- `feat: add new authentication provider`
- `fix: resolve OAuth redirect issue`
- `docs: update authentication setup guide`
- `refactor: improve component structure`

## 🔧 Development Environment

### Required Tools
- **Package Manager**: pnpm (not npm or yarn)
- **IDE**: VS Code recommended with TypeScript and ESLint extensions
- **Browser**: Chrome/Firefox with developer tools
- **Git**: Latest version with proper configuration

### Environment Configuration
See [Authentication Setup](../docs/AUTHENTICATION_SETUP.md) for detailed configuration of:
- Google OAuth credentials
- GitHub OAuth credentials
- NextAuth.js configuration
- Email provider setup (Resend)

## 🆘 Getting Help

### Common Issues
Check our [Troubleshooting Guide](../docs/TROUBLESHOOTING.md) for solutions to:
- Port conflicts and server issues
- Authentication and OAuth problems
- Git attribution and workflow issues
- Build and deployment problems

### Documentation Resources
- **Workflow Questions**: [AI Agent Workflow](../docs/AI_AGENT_WORKFLOW.md)
- **Setup Problems**: [Quick Start Guide](../docs/QUICK_START.md)
- **Command Help**: [Command Reference](../docs/COMMAND_REFERENCE.md)
- **Auth Issues**: [Authentication Setup](../docs/AUTHENTICATION_SETUP.md)

### Support Channels
- **GitHub Issues**: For bug reports and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Documentation**: Comprehensive guides in the `docs/` folder

## 📊 Project Roadmap

### Current Focus
- ✅ Google and GitHub OAuth integration
- ✅ AI agent collaboration workflows
- ✅ Comprehensive documentation

### Upcoming Features
- 🚧 Email magic link authentication (Resend integration)
- 🔮 WebAuthn/Passkeys (Auth.js v5, October 2025+)
- 🔮 Additional OAuth providers
- 🔮 Enhanced UI/UX improvements

## 🙏 Recognition

We appreciate all contributions to the SCE Portal project! Contributors will be recognized in:
- Git commit history with proper attribution
- Project documentation acknowledgments
- GitHub contributor statistics

## 📄 License

By contributing to this project, you agree that your contributions will be licensed under the same terms as the project.

---

**Ready to contribute?** Start with our [Quick Start Guide](../docs/QUICK_START.md) and review the [AI Agent Workflow](../docs/AI_AGENT_WORKFLOW.md) for detailed collaboration processes.
