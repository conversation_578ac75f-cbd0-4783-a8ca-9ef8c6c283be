import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
  prismaInitialized: boolean | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient();

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Initialize database on first use in production
let initPromise: Promise<void> | null = null;

export async function ensureDatabaseReady() {
  // Only initialize once per serverless function instance
  if (globalForPrisma.prismaInitialized) {
    return;
  }

  // Prevent multiple concurrent initializations
  if (!initPromise) {
    initPromise = initializeIfNeeded();
  }

  await initPromise;
}

async function initializeIfNeeded() {
  try {
    // In production with SQLite, ensure database exists
    if (process.env.NODE_ENV === 'production' && process.env.DATABASE_URL?.startsWith('file:')) {
      const { initializeDatabase } = await import('./database-init');
      await initializeDatabase();
    }

    globalForPrisma.prismaInitialized = true;
  } catch (error) {
    console.error('Database initialization failed:', error);
    // Reset so it can be retried
    initPromise = null;
    throw error;
  }
}
