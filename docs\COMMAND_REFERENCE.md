# Command Reference
## SCE Portal Development Commands

Quick reference for essential development commands, Git operations, and AI agent procedures.

## 📦 Package Management

### pnpm Commands (Required - Not npm/yarn)
```bash
# Installation
pnpm install                    # Install all dependencies
pnpm install package-name       # Add new package
pnpm add package-name           # Add new package (alias)
pnpm remove package-name        # Remove package
pnpm update                     # Update all packages
pnpm update package-name        # Update specific package

# Development Scripts
pnpm dev                        # Start development server
pnpm build                      # Build for production
pnpm start                      # Start production server
pnpm lint                       # Run ESLint
pnpm lint:fix                   # Fix ESLint errors automatically
pnpm type-check                 # TypeScript validation
pnpm test                       # Run test suite
pnpm test:watch                 # Run tests in watch mode
```

### Package Information
```bash
pnpm list                       # List installed packages
pnpm outdated                   # Check for outdated packages
pnpm why package-name           # Show why package is installed
```

## 🔧 Development Commands

### Server Management
```bash
# Start development server
pnpm dev                        # Default port 3000
pnpm dev -- -p 3001            # Custom port

# Production build and test
pnpm build                      # Build application
pnpm start                      # Start production server

# Kill port if needed
npx kill-port 3000             # Kill process on port 3000
```

### Code Quality
```bash
# Linting
pnpm lint                       # Check for lint errors
pnpm lint:fix                   # Auto-fix lint errors

# Type Checking
pnpm type-check                 # TypeScript validation

# Testing
pnpm test                       # Run all tests
pnpm test:watch                 # Watch mode
pnpm test:coverage              # With coverage report
```

## 🔀 Git Operations

### Basic Git Commands
```bash
# Repository management
git status                      # Check repository status
git log --oneline              # View commit history
git log --format=fuller        # Detailed commit info with attribution

# Branch management
git branch                      # List branches
git branch -a                   # List all branches (including remote)
git checkout branch-name        # Switch to branch
git checkout -b new-branch      # Create and switch to new branch
git branch -d branch-name       # Delete local branch
```

### AI Agent Git Attribution
```bash
# Commit with proper agent attribution
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "feat: your message"

# Amend commit with correct attribution
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit --amend --reset-author --no-edit

# Check current Git configuration
git config user.name
git config user.email

# Verify commit attribution
git log --format=fuller
```

### Standard Workflow Commands
```bash
# 1. Update main branch
git checkout main
git pull origin main

# 2. Create feature branch
git checkout -b feature/your-feature-name

# 3. Stage and commit changes
git add .
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "feat: implement feature"

# 4. Push branch
git push origin feature/your-feature-name

# 5. After PR merge, cleanup
git checkout main
git pull origin main
git branch -d feature/your-feature-name
```

### Advanced Git Operations
```bash
# Stash changes
git stash                       # Stash current changes
git stash pop                   # Apply and remove latest stash
git stash list                  # List all stashes

# Reset operations
git reset --soft HEAD~1         # Undo last commit, keep changes staged
git reset --hard HEAD~1         # Undo last commit, discard changes
git reset --hard origin/main    # Reset to match remote main

# Remote operations
git remote -v                   # Show remote URLs
git fetch origin                # Fetch latest changes
git pull origin main            # Pull and merge changes
git push --force origin branch  # Force push (use carefully)
```

## 🤖 AI Agent Specific Commands

### Environment Variable Setup
```bash
# Check environment variables
echo $AI_AGENT_NAME
echo $AI_AGENT_EMAIL
echo $AI_AGENT_PAT

# Set environment variables (if needed)
export AI_AGENT_NAME="Agent for Kevin Lonigro"
export AI_AGENT_EMAIL="<EMAIL>"
export AI_AGENT_PAT="your-github-pat"
```

### Agent Workflow Commands
```bash
# New Task Workflow
git checkout main
git pull origin main
git checkout -b feature/task-name

# Development and testing
pnpm dev                        # Test changes
pnpm lint                       # Check code quality
pnpm type-check                 # Validate TypeScript
pnpm test                       # Run tests
pnpm build                      # Verify build

# Commit with attribution
git add .
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "feat: implement task"

# Push and create PR
git push origin feature/task-name
```

### Attribution Verification
```bash
# Check commit attribution
git log --format=fuller -n 5

# Expected output format:
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

# Fix incorrect attribution
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit --amend --reset-author --no-edit
git push --force origin branch-name
```

## 🔍 Debugging Commands

### Development Debugging
```bash
# Check running processes
ps aux | grep node              # Find Node.js processes
lsof -i :3000                   # Check what's using port 3000

# Environment debugging
printenv | grep NEXT            # Show Next.js environment variables
printenv | grep AUTH            # Show auth-related variables

# Build debugging
pnpm build --debug              # Verbose build output
pnpm start --debug              # Debug production server
```

### Git Debugging
```bash
# Check repository status
git status --porcelain          # Machine-readable status
git diff                        # Show unstaged changes
git diff --staged               # Show staged changes
git diff HEAD~1                 # Compare with previous commit

# Remote debugging
git remote show origin          # Show remote information
git ls-remote origin            # List remote references
git fetch --dry-run             # Preview fetch operation
```

## 🧪 Testing Commands

### Test Execution
```bash
# Run all tests
pnpm test                       # Single run
pnpm test:watch                 # Watch mode
pnpm test:coverage              # With coverage

# Run specific tests
pnpm test -- --testNamePattern="Auth"
pnpm test -- --testPathPattern="components"

# Debug tests
pnpm test -- --verbose          # Verbose output
pnpm test -- --detectOpenHandles # Debug hanging tests
```

### Authentication Testing
```bash
# Start development server
pnpm dev

# Test authentication endpoints
curl http://localhost:3000/api/auth/providers
curl http://localhost:3000/api/auth/session

# Test with authentication
curl -H "Cookie: next-auth.session-token=..." http://localhost:3000/api/protected
```

## 🚀 Deployment Commands

### Build and Deploy
```bash
# Production build
pnpm build                      # Build application
pnpm start                      # Test production build locally

# Vercel deployment (if configured)
vercel                          # Deploy to Vercel
vercel --prod                   # Deploy to production

# Environment verification
pnpm build && pnpm start        # Full production test
```

### Pre-deployment Checklist
```bash
# Quality assurance
pnpm lint                       # ✅ No lint errors
pnpm type-check                 # ✅ No TypeScript errors
pnpm test                       # ✅ All tests pass
pnpm build                      # ✅ Build succeeds
pnpm start                      # ✅ Production server starts

# Git verification
git status                      # ✅ Clean working directory
git log --format=fuller -n 3    # ✅ Proper attribution
```

## 🔧 Utility Commands

### File Operations
```bash
# Find files
find . -name "*.tsx" -type f    # Find React components
find . -name "*.test.*" -type f # Find test files
grep -r "TODO" src/             # Find TODO comments

# File permissions
chmod +x script.sh              # Make script executable
ls -la                          # List files with permissions
```

### System Information
```bash
# Node.js and npm versions
node --version                  # Node.js version
pnpm --version                  # pnpm version
npx --version                   # npx version

# System information
pwd                             # Current directory
whoami                          # Current user
uname -a                        # System information (Unix/Linux)
```

## 📋 Command Cheat Sheet

### Daily Development
```bash
git checkout main && git pull origin main
git checkout -b feature/new-feature
pnpm dev
# Make changes
pnpm lint && pnpm type-check && pnpm test
git add . && git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "feat: description"
git push origin feature/new-feature
```

### Emergency Fixes
```bash
# Fix attribution
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit --amend --reset-author --no-edit
git push --force origin branch-name

# Kill stuck processes
npx kill-port 3000
pkill -f "next-dev"

# Reset to clean state
git stash && git checkout main && git pull origin main
```

---

**Need more context?** Check the [AI Agent Workflow](AI_AGENT_WORKFLOW.md) for detailed procedures and the [Troubleshooting Guide](TROUBLESHOOTING.md) for issue resolution.
