'use client';

import { signOut } from 'next-auth/react';
import styles from '../dashboard/LogoutButton.module.css';

export default function LogoutButton() {
  const handleLogout = () => {
    // Use NextAuth client-side signOut to bypass confirmation page
    // This prevents the logout confirmation from being added to browser history
    signOut({ callbackUrl: '/' });
  };

  return (
    <button
      onClick={handleLogout}
      data-logout="true"
      className={`${styles.logoutButton} logout-button bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 text-white dark:text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-red-400 border-0 cursor-pointer`}
    >
      Logout
    </button>
  );
}
