'use client';

import { SessionProvider } from 'next-auth/react';
import { useEffect } from 'react';

function ChunkErrorHandler({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Handle chunk load errors by refreshing the page
    const handleChunkError = (event: ErrorEvent) => {
      if (event.message?.includes('Loading chunk') ||
          event.message?.includes('ChunkLoadError')) {
        console.log('Chunk load error detected, refreshing page...');
        window.location.reload();
      }
    };

    // Handle unhandled promise rejections (chunk errors)
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (event.reason?.message?.includes('Loading chunk') ||
          event.reason?.name === 'ChunkLoadError') {
        console.log('Chunk load error detected, refreshing page...');
        window.location.reload();
      }
    };

    window.addEventListener('error', handleChunkError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleChunkError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return <>{children}</>;
}

export function NextAuthProvider({ children }: { children: React.ReactNode }) {
  return (
    <ChunkErrorHandler>
      <SessionProvider
        // Refetch session every 15 minutes (reduced frequency)
        refetchInterval={15 * 60}
        // Don't refetch on window focus during development to avoid conflicts
        refetchOnWindowFocus={false}
      >
        {children}
      </SessionProvider>
    </ChunkErrorHandler>
  );
}