# Project Memories

## Guideline Details

### App Router
App Router is preferred because it provides better performance and more intuitive routing.

### Server Components
Server Components is preferred to reduce client-side JavaScript and improve initial load times.

## Project History
- Started with Next.js 15.3.5
- Using Google OAuth for authentication
- Fixed authentication system mismatch: middleware and dashboard pages now use the same custom session system to prevent redirect errors

## Command Preferences
- Use direct pnpm commands (e.g., "pnpm dev", "pnpm build", etc) instead of prefixing with "cmd /c"
- In this Windows PowerShell environment, avoid PowerShell-specific commands or cmdlets (Get-Process, Set-ExecutionPolicy, etc.) for development tasks due to execution policy restrictions
- Use standard cross-platform commands or documented direct pnpm commands for package management, development servers, builds, and testing
- Note: The launch-process tool has output buffering issues that make pnpm commands appear to hang when using read-process, but commands execute successfully (verify with read-terminal)

## Development Workflow

### Local Agent Workflow
1. **Trigger**: When user starts message with "New Task", immediately create new branch from main
2. **Pre-Task**: Create new branch from main before starting work
   - **CRITICAL**: Always ensure main is up-to-date before branching:
     ```bash
     git checkout main
     git pull origin main  # Fetch latest changes
     git checkout -b new-feature-branch
     ```
   - **Why**: Prevents missing recent commits/fixes that were merged to main
   - **Example Issue**: Missing icon improvements, bug fixes, or other recent changes
3. **During Task**: Complete development and testing
4. **Post-Task**: Confirm with peer before proceeding with commit and PR to allow time for local review of changes
5. **After Confirmation**: Create commit and PR for deployment

### Remote Agent Workflow
- Continue with their existing workflow (no change from current process)

## Git Configuration

### Local Agent Configuration
- **Local Repository**: Configured with user credentials for clean IDE experience
- **Local AI Agent Operations**: Uses temporary credential overrides via `-c` flags for commits without affecting local config
- **Agent Email**: Use AI_AGENT_EMAIL environment variable value: `${AI_AGENT_EMAIL}`
- **Command Example**: `git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "message"`

### Remote Agent Configuration
- **Remote Agent Environment**: Uses template environment script (`.augment/env/nextjs-auth-environment.sh`) with placeholder values
- **Git Credentials**: Set via `git config --global` in environment setup script using environment variable references
- **Environment Variables**: Template uses `${VARIABLE_NAME:-default}` syntax for security
- **Limitations**: Remote agents may have limited functionality without proper secret injection
- **Security**: No actual secrets stored in repository - only templates and placeholders

## Environment Variable Security

### Hybrid Development/Production Approach
- **Template Files**: Repository contains only placeholder templates with `${VARIABLE_NAME:-default}` syntax
- **Local Development**: Use `.env.local` files (gitignored) with actual values for full functionality
- **Remote Agents**: Receive templates only - may have limited testing capabilities without secret injection
- **Production**: Uses secure environment variable injection (Vercel, etc.)

### Security Benefits
- ✅ No sensitive credentials in Git history
- ✅ Templates show required variables without exposing values
- ✅ Portable across projects without value updates
- ✅ Production-ready approach

### Trade-offs
- ❌ Remote agents cannot perform full API testing without secrets
- ❌ Some functionality may be limited in remote agent environments
- ✅ Acceptable for code changes, builds, and non-API functionality

## Remote Agent Post-Action Guidelines

### Reviewer Checklist (After Remote Agent Completes Task)

When a remote agent completes a task and creates a PR, the reviewer should perform these local actions:

#### 1. **API Testing** (if applicable)
- [ ] Pull the branch locally: `git checkout <branch-name>`
- [ ] Install dependencies: `pnpm install`
- [ ] Run development server: `pnpm dev`
- [ ] Test API endpoints that require authentication (OAuth, email, etc.)
- [ ] Verify email functionality (magic links, notifications)
- [ ] Test authentication flows (sign in, sign out, session management)

#### 2. **Attribution Verification**
- [ ] Check commit attribution: `git log --format=fuller`
- [ ] Verify commits show `${AI_AGENT_NAME} <${AI_AGENT_EMAIL}>` as author
- [ ] If attribution is incorrect, amend commits:
  ```bash
  git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit --amend --reset-author --no-edit
  git push --force origin <branch-name>
  ```

#### 3. **Environment Variable Validation**
- [ ] Ensure no hardcoded secrets were accidentally committed
- [ ] Verify template files use `${VARIABLE_NAME:-default}` syntax
- [ ] Check that `.env.local` is properly gitignored
- [ ] Confirm sensitive files are not in Git history

#### 4. **Full Integration Testing**
- [ ] Run complete test suite: `pnpm test`
- [ ] Verify build succeeds: `pnpm build`
- [ ] Test production build locally: `pnpm start`
- [ ] Check for TypeScript errors: `pnpm type-check`
- [ ] Run linting: `pnpm lint`

#### 5. **Security Review**
- [ ] Review any new environment variable usage
- [ ] Ensure no API keys or secrets in code comments
- [ ] Verify proper error handling doesn't expose sensitive data
- [ ] Check that new dependencies are from trusted sources

### When to Use Remote Agents vs Local Development

**✅ Good for Remote Agents:**
- UI/UX changes and styling
- Component development and refactoring
- TypeScript/JavaScript logic changes
- Build configuration updates
- Documentation updates
- Non-API functionality

**❌ Requires Local Testing:**
- OAuth provider integration
- Email functionality (magic links, notifications)
- Database operations requiring real connections
- Third-party API integrations
- Payment processing
- Real-time features (WebSockets, etc.)

## Git History Cleanup (Sensitive Data Removal)

### When to Clean Git History
- **Immediate Action Required**: If actual secrets (API keys, tokens, passwords) were committed
- **Security Priority**: Rotate/revoke exposed credentials FIRST, then clean history
- **Coordination Needed**: All collaborators must be informed and coordinate cleanup

### Recommended Method: git-filter-repo
GitHub officially recommends `git-filter-repo` over older tools like BFG Repo-Cleaner.

#### Installation
```bash
# macOS with Homebrew
brew install git-filter-repo

# Manual installation
pip install git-filter-repo
```

#### Usage for Specific Files
```bash
# Remove specific file from all history
git-filter-repo --sensitive-data-removal --invert-paths --path .augment/env/nextjs-auth-environment.sh

# Replace text patterns from all files
git-filter-repo --sensitive-data-removal --replace-text ../passwords.txt
```

#### Complete Process
1. **Install git-filter-repo** (version 2.47+ with `--sensitive-data-removal` flag)
2. **Clone repository locally** for cleanup
3. **Run git-filter-repo** to remove sensitive data
4. **Check affected PRs**: `grep -c '^refs/pull/.*/head$' .git/filter-repo/changed-refs`
5. **Force push changes**: `git push --force --mirror origin`
6. **Contact GitHub Support** to remove cached views and PR references
7. **Coordinate with collaborators** to clean their clones

### Important Considerations
- **⚠️ High Risk**: Easy to recontaminate if collaborators have old clones
- **⚠️ Breaking Changes**: Commit hashes change, breaking tooling/automation
- **⚠️ Lost Data**: PR diffs become unavailable, signatures removed
- **⚠️ Coordination**: All team members must clean their clones or re-clone

### Alternative: Accept the Risk
For development-only secrets in learning projects:
- Rotate the exposed credentials immediately
- Document the incident and lessons learned
- Implement better practices going forward
- Consider if history cleanup is worth the complexity
- **Authentication**: Remote agents use GitHub OAuth integration configured in VS Code settings, not environment variables

## Screen Captures
- **Location**: `/screen_captures` folder in project root
- **Purpose**: AI agent saves screenshots here for user visibility during development and testing
- **Maintenance**: User cleans up folder periodically
- **Git**: Folder is ignored in `.gitignore` to prevent committing temporary screenshots

## Augment Configuration

### Auto Execution Settings
- **Issue**: Excessive approval prompting for every tool/command execution
- **Solution**: Check that "Auto run without approval prompting" option is enabled in Augment project settings
- **Location**: This toogle setting is on the Augment prompt panel
- **Impact**: When disabled, every tool call (git commands, file operations, etc.) will prompt for approval
- **Note**: This is a project-level setting that can be overlooked when creating new projects

## Future Reminders
- **October 2025**: Revisit Auth.js v5 to reevaluate WebAuthn/Passkey implementation options for the NextAuth.js project, as v4 lacks WebAuthn support and v5 should be stable by then

