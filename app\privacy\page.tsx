import type { Metadata } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Privacy Policy | St Cloud Enterprises Portal",
  description: "Privacy Policy for St Cloud Enterprises Portal - Learn how we collect, use, and protect your personal information in our comprehensive portal providing access to website properties managed by St Cloud Enterprises..",
  robots: "index, follow",
  keywords: "privacy policy, data protection, authentication, OAuth, Google, GitHub, Amazon, NextAuth.js, GDPR, privacy rights",
  authors: [{ name: "St Cloud Enterprises" }],
  creator: "St Cloud Enterprises",
  publisher: "St Cloud Enterprises",
  openGraph: {
    title: "Privacy Policy | St Cloud Enterprises Portal",
    description: "Privacy Policy for St Cloud Enterprises Portal - Learn how we collect, use, and protect your personal information in our comprehensive portal providing access to website properties managed by St Cloud Enterprises..",
    type: "website",
    locale: "en_US",
    siteName: "St Cloud Enterprises Portal",
  },
  twitter: {
    card: "summary",
    title: "Privacy Policy | St Cloud Enterprises Portal",
    description: "Privacy Policy for St Cloud Enterprises Portal - Learn how we collect, use, and protect your personal information in our comprehensive portal providing access to website properties managed by St Cloud Enterprises..",
  },
};

export default function PrivacyPolicy() {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white shadow-sm rounded-lg p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Privacy Policy</h1>
            <p className="text-gray-600">Last updated: July 26, 2025</p>
          </div>

          {/* Content */}
          <div className="prose prose-gray max-w-none">
            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">1. Introduction</h2>
              <p className="text-gray-700 mb-4">
                St Cloud Enterprises Portal (&quot;we,&quot; &quot;our,&quot; or &quot;us&quot;) is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our authentication service.
              </p>
              <p className="text-gray-700 mb-4">
                This portal provides access to website properties managed by St Cloud Enterprises. We take your privacy seriously and have implemented appropriate technical and organizational measures to protect your personal information.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">2. Information We Collect</h2>
              
              <h3 className="text-xl font-medium text-gray-900 mb-3">2.1 Information from OAuth Providers</h3>
              <p className="text-gray-700 mb-4">
                When you sign in using third-party OAuth providers, we collect:
              </p>
              <ul className="list-disc pl-6 text-gray-700 mb-4">
                <li><strong>Google OAuth:</strong> Email address, full name, profile picture URL</li>
                <li><strong>GitHub OAuth:</strong> Email address, username, full name, profile picture URL</li>
                <li><strong>Amazon OAuth:</strong> Email address, full name, profile information</li>
              </ul>

              <h3 className="text-xl font-medium text-gray-900 mb-3">2.2 Email Authentication</h3>
              <p className="text-gray-700 mb-4">
                When you use passwordless email authentication, we collect:
              </p>
              <ul className="list-disc pl-6 text-gray-700 mb-4">
                <li>Your email address</li>
                <li>Authentication timestamps</li>
                <li>Magic link usage data</li>
              </ul>

              <h3 className="text-xl font-medium text-gray-900 mb-3">2.3 Automatically Collected Information</h3>
              <p className="text-gray-700 mb-4">
                We automatically collect certain information when you use our service:
              </p>
              <ul className="list-disc pl-6 text-gray-700 mb-4">
                <li>Session information and authentication tokens</li>
                <li>Login timestamps and session duration</li>
                <li>Browser type and version (for compatibility)</li>
                <li>Device information (for security purposes)</li>
                <li>IP address (for security and fraud prevention)</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">3. How We Use Your Information</h2>
              <p className="text-gray-700 mb-4">
                We use the collected information for the following purposes:
              </p>
              <ul className="list-disc pl-6 text-gray-700 mb-4">
                <li><strong>Authentication:</strong> To verify your identity and provide secure access to your account</li>
                <li><strong>User Experience:</strong> To personalize your experience and display your profile information</li>
                <li><strong>Security:</strong> To detect and prevent fraud, abuse, and unauthorized access</li>
                <li><strong>Communication:</strong> To send you authentication emails and important service updates</li>
                <li><strong>Service Improvement:</strong> To analyze usage patterns and improve our services</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">4. Data Storage and Security</h2>
              
              <h3 className="text-xl font-medium text-gray-900 mb-3">4.1 Data Storage</h3>
              <p className="text-gray-700 mb-4">
                Your information is stored in:
              </p>
              <ul className="list-disc pl-6 text-gray-700 mb-4">
                <li>A secure database for session management and user accounts</li>
                <li>Encrypted session storage for enhanced security</li>
                <li>Temporary authentication tokens with automatic expiration</li>
              </ul>

              <h3 className="text-xl font-medium text-gray-900 mb-3">4.2 Security Measures</h3>
              <p className="text-gray-700 mb-4">
                We implement appropriate security measures including:
              </p>
              <ul className="list-disc pl-6 text-gray-700 mb-4">
                <li>Encrypted data transmission using HTTPS</li>
                <li>Secure session management with automatic expiration</li>
                <li>Regular security updates and dependency management</li>
                <li>Access controls and authentication middleware</li>
                <li>Secure handling of authentication tokens</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">5. Data Retention</h2>
              <p className="text-gray-700 mb-4">
                We retain your personal information for as long as necessary to:
              </p>
              <ul className="list-disc pl-6 text-gray-700 mb-4">
                <li>Provide you with our services</li>
                <li>Comply with legal obligations</li>
                <li>Resolve disputes and enforce our agreements</li>
                <li>Maintain security and prevent fraud</li>
              </ul>
              <p className="text-gray-700 mb-4">
                As our service is concerned with efficieny we may periodically change our data. We will provide reasonable notice if our action affects your account.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">6. Your Rights and Choices</h2>
              <p className="text-gray-700 mb-4">
                You have the following rights regarding your personal information:
              </p>
              <ul className="list-disc pl-6 text-gray-700 mb-4">
                <li><strong>Access:</strong> Request a copy of the personal information we hold about you</li>
                <li><strong>Correction:</strong> Request correction of inaccurate or incomplete information</li>
                <li><strong>Deletion:</strong> Request deletion of your personal information</li>
                <li><strong>Portability:</strong> Request a copy of your data in a machine-readable format</li>
                <li><strong>Withdrawal of Consent:</strong> Withdraw consent for data processing where applicable</li>
                <li><strong>Account Termination:</strong> Delete your account and associated data</li>
              </ul>
              <p className="text-gray-700 mb-4">
                To exercise these rights, please contact us using the information provided in Section 10.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">7. Third-Party Services</h2>
              
              <h3 className="text-xl font-medium text-gray-900 mb-3">7.1 OAuth Providers</h3>
              <p className="text-gray-700 mb-4">
                We integrate with third-party OAuth providers. Please review their privacy policies:
              </p>
              <ul className="list-disc pl-6 text-gray-700 mb-4">
                <li><a href="https://policies.google.com/privacy" className="text-blue-600 hover:text-blue-500" target="_blank" rel="noopener noreferrer">Google Privacy Policy</a></li>
                <li><a href="https://docs.github.com/en/site-policy/privacy-policies/github-privacy-statement" className="text-blue-600 hover:text-blue-500" target="_blank" rel="noopener noreferrer">GitHub Privacy Statement</a></li>
                <li><a href="https://aws.amazon.com/privacy/" className="text-blue-600 hover:text-blue-500" target="_blank" rel="noopener noreferrer">Amazon Privacy Notice</a></li>
              </ul>

              <h3 className="text-xl font-medium text-gray-900 mb-3">7.2 Email Service</h3>
              <p className="text-gray-700 mb-4">
                We use Resend for sending authentication emails. Please review their <a href="https://resend.com/legal/privacy-policy" className="text-blue-600 hover:text-blue-500" target="_blank" rel="noopener noreferrer">Privacy Policy</a>.
              </p>

              <h3 className="text-xl font-medium text-gray-900 mb-3">7.3 NextAuth.js</h3>
              <p className="text-gray-700 mb-4">
                Our authentication system is built on NextAuth.js. Learn more about their <a href="https://next-auth.js.org/getting-started/introduction#security" className="text-blue-600 hover:text-blue-500" target="_blank" rel="noopener noreferrer">security practices</a>.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">8. International Data Transfers</h2>
              <p className="text-gray-700 mb-4">
                Your information may be transferred to and processed in countries other than your own. We ensure that such transfers comply with applicable data protection laws and implement appropriate safeguards.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">9. Changes to This Privacy Policy</h2>
              <p className="text-gray-700 mb-4">
                We may update this Privacy Policy from time to time. We will notify you of any material changes by posting the new Privacy Policy on this page and updating the &quot;Last updated&quot; date.
              </p>
              <p className="text-gray-700 mb-4">
                We encourage you to review this Privacy Policy periodically to stay informed about how we protect your information.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">10. Contact Information</h2>
              <p className="text-gray-700 mb-4">
                If you have any questions about this Privacy Policy or our data practices, please contact us at:
              </p>
              <div className="bg-gray-50 p-4 rounded-md">
                <p className="text-gray-700 mb-2">
                  <strong>Email:</strong> info at stcloudenterprises.com
                </p>
                <p className="text-gray-700">
                  <strong>Response Time:</strong> We aim to respond to privacy inquiries within 30 days.
                </p>
              </div>
            </section>
          </div>

          {/* Navigation */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <Link
                href="/terms"
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                View Terms of Service →
              </Link>
              <Link
                href="/"
                className="inline-flex items-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-500 transition-colors"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                  />
                </svg>
                Return to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
