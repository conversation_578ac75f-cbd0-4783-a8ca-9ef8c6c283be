# Authentication Setup Guide
## SCE Portal OAuth Provider Configuration

This guide covers detailed setup for all authentication providers in the SCE Portal project.

## 🔐 Overview

The SCE Portal uses NextAuth.js with multiple authentication providers:
- **Google OAuth** (Primary)
- **GitHub OAuth** (Secondary)
- **Email Magic Links** (Planned)
- **WebAuthn/Passkeys** (Future - Auth.js v5)

## 🌐 Environment Variables

### Template Structure
All environment variables use template syntax for security:
```bash
VARIABLE_NAME="${VARIABLE_NAME:-default-value}"
```

### Complete Configuration
Create `.env.local` with actual values:

```bash
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-here

# Google OAuth Provider
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# GitHub OAuth Provider
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Email Provider (Resend)
RESEND_API_KEY=your-resend-api-key
RESEND_DOMAIN=your-domain.com
RESEND_SENDER=<EMAIL>
RESEND_RECIPIENT=<EMAIL>

# AI Agent Configuration
AI_AGENT_NAME="Agent for Kevin Lonigro"
AI_AGENT_EMAIL="<EMAIL>"
AI_AGENT_PAT=your-github-personal-access-token
GH_TOKEN="${AI_AGENT_PAT}"
```

## 🔵 Google OAuth Setup

### 1. Google Cloud Console Configuration
1. Visit [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing project
3. Enable Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"

### 2. OAuth Client Configuration
```
Application Type: Web application
Name: SCE Portal
Authorized JavaScript origins: http://localhost:3000
Authorized redirect URIs: http://localhost:3000/api/auth/callback/google
```

### 3. Environment Variables
```bash
GOOGLE_CLIENT_ID=123456789-abcdefghijklmnop.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-your-secret-here
```

### 4. Production Configuration
For production deployment, add:
```
Authorized JavaScript origins: https://your-domain.com
Authorized redirect URIs: https://your-domain.com/api/auth/callback/google
```

## 🐙 GitHub OAuth Setup

### 1. GitHub Developer Settings
1. Visit [GitHub Developer Settings](https://github.com/settings/developers)
2. Click "New OAuth App"
3. Fill in application details

### 2. OAuth App Configuration
```
Application name: SCE Portal
Homepage URL: http://localhost:3000
Authorization callback URL: http://localhost:3000/api/auth/callback/github
```

### 3. Environment Variables
```bash
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
```

### 4. Production Configuration
For production deployment, update:
```
Homepage URL: https://your-domain.com
Authorization callback URL: https://your-domain.com/api/auth/callback/github
```

## 📧 Email Magic Links (Resend)

### 1. Resend Account Setup
1. Create account at [Resend](https://resend.com/)
2. Verify your domain
3. Generate API key

### 2. Domain Verification
```bash
# Add DNS records for your domain
TXT record: resend._domainkey.yourdomain.com
Value: [provided by Resend]
```

### 3. Environment Variables
```bash
RESEND_API_KEY=re_your-api-key-here
RESEND_DOMAIN=yourdomain.com
RESEND_SENDER=<EMAIL>
RESEND_RECIPIENT=<EMAIL>  # For testing
```

### 4. Testing Email Functionality
```bash
# Start development server
pnpm dev

# Test email sending
# Navigate to sign-in page
# Enter email address
# Check for magic link email
```

## 🔑 NextAuth Secret Generation

### Generate Secure Secret
```bash
# Option 1: OpenSSL
openssl rand -base64 32

# Option 2: Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

# Option 3: Online generator
# Visit: https://generate-secret.vercel.app/32
```

### Environment Configuration
```bash
NEXTAUTH_SECRET=your-generated-secret-here
```

## 🚀 Production Deployment

### Vercel Environment Variables
1. Go to Vercel Dashboard → Project Settings → Environment Variables
2. Add all production environment variables
3. Ensure no template syntax in production values

### Production Checklist
- [ ] Update OAuth redirect URIs for production domain
- [ ] Generate new NEXTAUTH_SECRET for production
- [ ] Verify all environment variables are set
- [ ] Test authentication flows in production
- [ ] Monitor authentication logs

## 🧪 Testing Authentication

### Local Testing Procedure
```bash
# 1. Start development server
pnpm dev

# 2. Navigate to authentication page
open http://localhost:3000/auth/signin

# 3. Test each provider
# - Click "Sign in with Google"
# - Click "Sign in with GitHub"
# - Test email magic link (if configured)

# 4. Verify session creation
# - Check browser developer tools
# - Verify session cookies
# - Test protected routes
```

### Debugging Authentication Issues

#### Common Problems
1. **Invalid Client ID/Secret**
   - Verify credentials in provider console
   - Check for typos in environment variables
   - Ensure no extra spaces or characters

2. **Redirect URI Mismatch**
   - Verify callback URLs match exactly
   - Check for http vs https
   - Ensure port numbers match

3. **NEXTAUTH_URL Issues**
   - Must match actual development server URL
   - Include port number if not 3000
   - Update when changing development ports

#### Debug Commands
```bash
# Check environment variables
echo $NEXTAUTH_URL
echo $GOOGLE_CLIENT_ID

# Verify NextAuth configuration
# Check browser network tab for auth requests
# Review server logs for error messages
```

## 🔒 Security Best Practices

### Environment Variable Security
- ✅ Never commit actual secrets to Git
- ✅ Use template syntax in repository files
- ✅ Keep `.env.local` in `.gitignore`
- ✅ Rotate secrets regularly
- ✅ Use different secrets for development/production

### OAuth Security
- ✅ Restrict redirect URIs to known domains
- ✅ Use HTTPS in production
- ✅ Implement proper CSRF protection
- ✅ Validate OAuth state parameters
- ✅ Monitor authentication logs

### Session Security
- ✅ Use secure session cookies
- ✅ Implement proper session expiration
- ✅ Validate sessions on protected routes
- ✅ Clear sessions on sign out
- ✅ Use secure NEXTAUTH_SECRET

## 🔮 Future Authentication Plans

### WebAuthn/Passkeys (October 2025+)
- **Timeline**: Auth.js v5 stable release
- **Benefits**: Passwordless, phishing-resistant authentication
- **Implementation**: Will replace current password-based flows
- **Preparation**: Monitor Auth.js v5 development progress

### Additional Providers
Potential future integrations:
- Microsoft Azure AD
- Apple Sign-In
- Amazon OAuth
- Custom SAML providers

## 📚 Additional Resources

### Documentation Links
- [NextAuth.js Documentation](https://next-auth.js.org/)
- [Google OAuth 2.0 Guide](https://developers.google.com/identity/protocols/oauth2)
- [GitHub OAuth Apps](https://docs.github.com/en/developers/apps/building-oauth-apps)
- [Resend Documentation](https://resend.com/docs)

### Troubleshooting Resources
- [NextAuth.js Troubleshooting](https://next-auth.js.org/getting-started/upgrade-guide)
- [OAuth 2.0 Security Best Practices](https://datatracker.ietf.org/doc/html/draft-ietf-oauth-security-topics)

---

**Need help with setup?** Check the [Troubleshooting Guide](TROUBLESHOOTING.md) for common authentication issues and solutions.

**Ready to develop?** Return to the [Quick Start Guide](QUICK_START.md) to continue with development setup.
