# SCE Portal
## Next.js Authentication & AI Agent Collaboration Project

> ## 🚧 **Temporary Project Status Update**
>
> **⚠️ IMPORTANT NOTICE:** Stopped work on the learning project that I was previously working over the last month. Current project is a web portal for the St Cloud Enterprises project providing a consolidated dashboard page showing website properties managed by St Cloud Enterprises.
>
> The learning project was the source for the St Cloud Enterprises project. **First priority is to update content and features to better fit the current project.**
>
> *This notice will be removed once the project stabilizes.*

St Cloud Enterprises Portal - A modern web application featuring comprehensive authentication and AI agent collaboration workflows.

## 🚀 Quick Start

```bash
# Clone and setup
git clone https://github.com/stcloudenterprises/sce_portal.git
cd sce_portal
pnpm install

# Configure environment
cp .env.example .env.local
# Add your OAuth credentials to .env.local

# Start development
pnpm dev
```

Visit: http://localhost:3000

## 📚 Documentation

**Complete documentation is available in the [`docs/`](docs/) folder:**

- **[📖 Documentation Index](docs/README.md)** - Start here for navigation
- **[⚡ Quick Start Guide](docs/QUICK_START.md)** - Get up and running in 15 minutes
- **[🔐 Authentication Setup](docs/AUTHENTICATION_SETUP.md)** - OAuth provider configuration
- **[🤖 AI Agent Workflow](docs/AI_AGENT_WORKFLOW.md)** - Comprehensive collaboration guide
- **[💻 Command Reference](docs/COMMAND_REFERENCE.md)** - Essential development commands
- **[🔧 Troubleshooting](docs/TROUBLESHOOTING.md)** - Common issues and solutions

## 🛠️ Technology Stack

- **Framework**: Next.js 15.3.5 with App Router
- **Authentication**: NextAuth.js with Google & GitHub OAuth
- **Styling**: Tailwind CSS
- **Package Manager**: pnpm (required)
- **Environment**: Windows PowerShell compatible

## 🔐 Authentication Providers

- ✅ **Google OAuth** - Primary authentication
- ✅ **GitHub OAuth** - Secondary authentication
- 🚧 **Email Magic Links** - Planned (Resend integration)
- 🔮 **WebAuthn/Passkeys** - Future (Auth.js v5, October 2025+)

## 🤖 AI Agent Integration

This project features advanced AI agent collaboration workflows:

- **Local Agents**: Full API testing and sensitive operations
- **Remote Agents**: Code changes and UI development
- **Proper Attribution**: Clear Git history for human vs AI contributions
- **Security First**: Template-based environment variables

## 📋 Development Commands

```bash
# Package management (use pnpm only)
pnpm install                    # Install dependencies
pnpm dev                        # Start development server
pnpm build                      # Build for production
pnpm test                       # Run test suite
pnpm lint                       # Check code quality

# Git operations with AI agent attribution
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "message"
```

## 🔄 Development Workflow

1. **Branch Creation**: `git checkout -b feature/name`
2. **Development**: Implement features with testing
3. **Quality Assurance**: `pnpm lint && pnpm type-check && pnpm test`
4. **Build Verification**: `pnpm build`
5. **Pull Request**: Create PR for review and deployment

## 🆘 Need Help?

- **Setup Issues**: Check [Quick Start Guide](docs/QUICK_START.md)
- **Authentication Problems**: See [Authentication Setup](docs/AUTHENTICATION_SETUP.md)
- **Common Errors**: Review [Troubleshooting Guide](docs/TROUBLESHOOTING.md)
- **Command Help**: Reference [Command Reference](docs/COMMAND_REFERENCE.md)

## 📄 License

This project is part of the SCE Portal learning initiative.

---

**For comprehensive documentation and workflows, visit the [`docs/`](docs/) folder.**
