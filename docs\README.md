# SCE Portal Documentation

Welcome to the SCE Portal project documentation. This directory contains comprehensive guides for developers, AI agents, and project maintainers.

## 📚 Documentation Index

### Core Workflow Documentation
- **[AI Agent Workflow](AI_AGENT_WORKFLOW.md)** - Comprehensive collaboration guide between AI agents and human reviewers
- **[Quick Start Guide](QUICK_START.md)** - Essential setup and workflow overview for new team members
- **[Authentication Setup](AUTHENTICATION_SETUP.md)** - Detailed authentication configuration and provider setup

### Reference Materials
- **[Command Reference](COMMAND_REFERENCE.md)** - Essential development commands and Git operations
- **[Troubleshooting Guide](TROUBLESHOOTING.md)** - Common issues, solutions, and debugging procedures

## 🚀 Getting Started

### For New Developers
1. Start with the **[Quick Start Guide](QUICK_START.md)** for immediate setup
2. Review **[Authentication Setup](AUTHENTICATION_SETUP.md)** for environment configuration
3. Reference **[AI Agent Workflow](AI_AGENT_WORKFLOW.md)** for detailed collaboration processes

### For AI Agents
1. Review **[AI Agent Workflow](AI_AGENT_WORKFLOW.md)** sections 2-4 for authentication and capabilities
2. Follow **[Command Reference](COMMAND_REFERENCE.md)** for proper Git attribution
3. Consult **[Troubleshooting Guide](TROUBLESHOOTING.md)** for common issues

### For Project Maintainers
1. **[AI Agent Workflow](AI_AGENT_WORKFLOW.md)** contains complete historical context
2. **[Authentication Setup](AUTHENTICATION_SETUP.md)** documents all provider configurations
3. Review all documents quarterly for updates and accuracy

## 🏗️ Project Architecture

### Technology Stack
- **Framework**: Next.js 15.3.5 with App Router
- **Authentication**: NextAuth.js with multiple providers
- **Styling**: Tailwind CSS
- **Package Manager**: pnpm (required)
- **Environment**: Windows PowerShell

### Authentication Providers
- ✅ Google OAuth (Primary)
- ✅ GitHub OAuth (Secondary)
- 🚧 Email Magic Links (Planned)
- 🔮 WebAuthn/Passkeys (Auth.js v5, October 2025+)

## 🔐 Security & Best Practices

### Environment Variables
- **Local Development**: Use `.env.local` with actual values
- **Repository**: Template files with `${VARIABLE_NAME:-default}` syntax
- **Production**: Secure environment variable injection

### Git Attribution
- **AI Agent**: `agent-kevinlonigro <<EMAIL>>`
- **Human Reviewer**: `kevinlonigro <<EMAIL>>`

## 🔄 Development Workflow

### Standard Process
1. **Branch Creation**: Create from updated main
2. **Development**: Implement features with proper testing
3. **Quality Assurance**: Run complete test suite
4. **Pull Request**: Create PR for review
5. **Review & Merge**: Human validation and deployment

### Agent Types
- **Local Agents**: Full API testing, sensitive operations
- **Remote Agents**: Code changes, UI/UX, non-API functionality

## 📋 Quick Commands

```bash
# Development
pnpm dev                    # Start development server
pnpm build                  # Build for production
pnpm test                   # Run test suite

# Git with Agent Attribution
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "message"

# Branch Management
git checkout main && git pull origin main
git checkout -b feature/branch-name
```

## 🆘 Need Help?

- **Common Issues**: Check [Troubleshooting Guide](TROUBLESHOOTING.md)
- **Commands**: Reference [Command Reference](COMMAND_REFERENCE.md)
- **Setup Problems**: Review [Authentication Setup](AUTHENTICATION_SETUP.md)
- **Workflow Questions**: Consult [AI Agent Workflow](AI_AGENT_WORKFLOW.md)

## 📝 Contributing to Documentation

Documentation improvements are welcome! Please:
1. Follow the existing structure and formatting
2. Update relevant sections when making changes
3. Test all commands and procedures before documenting
4. Submit changes via pull request for review

## 🔄 Maintenance Schedule

- **Monthly**: Review for accuracy and updates
- **Quarterly**: Comprehensive review and reorganization
- **After Major Changes**: Update affected documentation immediately

---

**Last Updated**: July 30, 2025  
**Maintainer**: Kevin Lonigro  
**Project**: SCE Portal  
**Repository**: [stcloudenterprises/sce_portal](https://github.com/stcloudenterprises/sce_portal)
