{"name": "st-cloud-enterprises-portal", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit --pretty", "type-check:watch": "tsc --noEmit --watch --pretty", "type-check:verbose": "npx tsx scripts/type-check.ts --verbose", "type-check:files": "npx tsx scripts/type-check.ts --verbose --watch", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "cleanup-sessions": "npx tsx scripts/cleanup-sessions.ts", "git:cleanup-branches": "npx tsx scripts/git-cleanup-branches.ts", "git:list-branches": "git branch -a", "git:prune-remote": "git remote prune origin", "postinstall": "prisma generate"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.12.0", "next": "15.3.5", "next-auth": "^4.24.5", "prisma": "^6.12.0", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.6.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}