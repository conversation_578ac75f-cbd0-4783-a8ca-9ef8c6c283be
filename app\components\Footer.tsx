import Link from "next/link";

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-white border-t border-gray-200 mt-auto">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          {/* Left side - Project info */}
          <div className="flex flex-col items-center md:items-start">
            <p className="text-sm text-gray-600">
              St Cloud Enterprises Portal
            </p>
            <p className="text-xs text-gray-500">
              A comprehensive portal providing access to website properties managed by St Cloud Enterprises.
            </p>
          </div>

          {/* Center - Legal links */}
          <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6">
            <Link
              href="/privacy"
              className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
            >
              Privacy Policy
            </Link>
            <Link
              href="/terms"
              className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
            >
              Terms of Service
            </Link>
          </div>

          {/* Right side - Copyright and GitHub */}
          <div className="flex flex-col items-center md:items-end">
            <p className="text-xs text-gray-500">
              © {currentYear} St Cloud Enterprises
            </p>
            <div className="flex items-center space-x-4 mt-1">
              <a
                href="https://github.com/stcloudenterprises/sce_portal"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-gray-600 transition-colors"
                aria-label="View project on GitHub"
              >
                <svg
                  className="w-4 h-4"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
                    clipRule="evenodd"
                  />
                </svg>
              </a>
            </div>
          </div>
        </div>

        {/* Mobile-optimized layout for very small screens */}
        <div className="block sm:hidden mt-4 pt-4 border-t border-gray-100">
          <div className="text-center space-y-2">
            <div className="flex justify-center space-x-4">
              <Link
                href="/privacy"
                className="text-xs text-gray-600 hover:text-gray-900 transition-colors"
              >
                Privacy
              </Link>
              <span className="text-xs text-gray-400">•</span>
              <Link
                href="/terms"
                className="text-xs text-gray-600 hover:text-gray-900 transition-colors"
              >
                Terms
              </Link>
              <span className="text-xs text-gray-400">•</span>
              <a
                href="https://github.com/stcloudenterprises/sce_portal"
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs text-gray-600 hover:text-gray-900 transition-colors"
              >
                GitHub
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
