import { NextResponse } from 'next/server';
import { cleanupExpiredSessions } from '@/app/lib/session-cleanup';

/**
 * Vercel Cron Job endpoint for session cleanup
 * This endpoint is automatically called by Vercel's cron system
 * 
 * Security: Vercel cron jobs include a special header for authentication
 */
export async function GET(request: Request) {
  // Verify this is a legitimate Vercel cron request
  const authHeader = request.headers.get('authorization');
  if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }

  try {
    console.log('Starting scheduled session cleanup...');
    
    const result = await cleanupExpiredSessions();
    
    console.log('Scheduled cleanup completed:', result);
    
    return NextResponse.json({
      message: 'Scheduled session cleanup completed',
      result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Scheduled cleanup failed:', error);
    return NextResponse.json(
      { 
        error: 'Scheduled cleanup failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Also support POST for manual testing in development
export async function POST(request: Request) {
  // Only allow manual POST in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'Manual cleanup not available in production' },
      { status: 403 }
    );
  }

  return GET(request);
}
